# Installation Guide - Cursorful Screen Recorder

## Quick Start

### 1. Load the Extension

1. Open Google Chrome
2. Navigate to `chrome://extensions/`
3. Enable **Developer mode** (toggle in the top-right corner)
4. Click **"Load unpacked"**
5. Select the `chromerecord` folder containing the extension files
6. The Cursorful icon should appear in your extensions toolbar

### 2. Grant Permissions

When you first use the extension:
1. Click the Cursorful icon in your toolbar
2. Select your recording source (tab, window, or screen)
3. Click "Select screen"
4. Chrome will prompt for screen sharing permissions - click **"Allow"**

### 3. Start Recording

1. Choose your recording source
2. Follow the three-step process in the interface
3. Click "Start recording" to begin
4. Use the controls to pause or stop recording
5. Download your recording when complete

## Troubleshooting

### Extension Not Loading
- Ensure all files are in the same directory
- Check that `manifest.json` is present and valid
- Refresh the extensions page and try again

### Recording Not Starting
- Make sure you granted screen capture permissions
- Try refreshing the page and starting again
- Check browser console for error messages

### No Audio in Recording
- Some sources may not support audio capture
- The extension will automatically fallback to video-only recording
- System audio capture depends on the selected source

### Cursor Tracking Not Working
- Cursor tracking works best on regular web pages
- Some sites may block content scripts
- Try the included `test-page.html` for testing

## Testing the Extension

1. Open the included `test-page.html` in Chrome
2. Load the extension following the steps above
3. Start a recording and interact with the test page elements
4. Verify cursor tracking and click indicators work
5. Stop recording and download the file

## Browser Requirements

- Google Chrome 88 or later
- Chromium-based browsers with extension support
- Screen capture API support

## File Structure Verification

Ensure your directory contains these files:
```
chromerecord/
├── manifest.json
├── background.js
├── recorder.html
├── recorder.css
├── recorder.js
├── content.js
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
├── test-page.html
├── README.md
└── INSTALLATION.md
```

## Next Steps

After successful installation:
1. Test basic recording functionality
2. Try different recording sources (tab, window, screen)
3. Test cursor tracking on various websites
4. Experiment with the recording controls
5. Check recording quality and file output

## Support

If you encounter issues:
1. Check the browser console for errors
2. Verify all permissions are granted
3. Try reloading the extension
4. Test with the provided test page
5. Ensure you're using a supported Chrome version

## Development Mode

For development and testing:
1. Keep Developer mode enabled
2. Use "Reload" button in chrome://extensions/ after making changes
3. Check background script logs in the extension details
4. Use Chrome DevTools for debugging the recorder interface
