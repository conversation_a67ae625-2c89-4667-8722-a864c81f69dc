<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursorful Screen Recorder</title>
    <link rel="stylesheet" href="recorder.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <span class="logo-text">cursorful</span>
            </div>
        </header>

        <main class="main-content">
            <h1 class="title">Record your browser in three simple steps</h1>
            
            <div class="steps-container">
                <!-- Step 1: Share screen -->
                <div class="step-card">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3 class="step-title">Share this screen</h3>
                        <p class="step-description">Make sure to select the screen that is displaying this browser window.</p>
                        
                        <div class="recording-options">
                            <button class="option-btn" id="selectTab" data-source="tab">
                                <span class="option-icon">🗂️</span>
                                <span class="option-text">Select tab</span>
                            </button>
                            <button class="option-btn" id="selectWindow" data-source="window">
                                <span class="option-icon">🪟</span>
                                <span class="option-text">Select window</span>
                            </button>
                            <button class="option-btn" id="selectScreen" data-source="screen">
                                <span class="option-icon">🖥️</span>
                                <span class="option-text">Select screen</span>
                            </button>
                        </div>
                        
                        <button class="primary-btn" id="selectScreenBtn" disabled>
                            <span class="btn-icon">📺</span>
                            Select screen
                        </button>
                    </div>
                </div>

                <!-- Step 2: Hide sharing bar -->
                <div class="step-card" id="step2" style="opacity: 0.5;">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3 class="step-title">Hide screen sharing bar</h3>
                        <p class="step-description">Unless hidden, the bar at the bottom will appear in your recording. If the bar appears on another screen, you can simply ignore it.</p>
                    </div>
                </div>

                <!-- Step 3: Start recording -->
                <div class="step-card" id="step3" style="opacity: 0.5;">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3 class="step-title">Start recording</h3>
                        <p class="step-description">You will be taken to your previous tab and recording will start immediately. Stop the recording from this pinned tab or by pressing Cursorful's button in the extensions toolbar.</p>
                        
                        <button class="primary-btn success" id="startRecordingBtn" disabled>
                            <span class="btn-icon">⏺️</span>
                            Start recording
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recording controls (hidden initially) -->
            <div class="recording-controls" id="recordingControls" style="display: none;">
                <div class="recording-status">
                    <span class="recording-indicator"></span>
                    <span class="recording-text">Recording...</span>
                    <span class="recording-timer" id="recordingTimer">00:00</span>
                </div>
                <div class="control-buttons">
                    <button class="control-btn" id="pauseBtn">
                        <span class="btn-icon">⏸️</span>
                        Pause
                    </button>
                    <button class="control-btn danger" id="stopBtn">
                        <span class="btn-icon">⏹️</span>
                        Stop
                    </button>
                </div>
            </div>

            <!-- Download section (hidden initially) -->
            <div class="download-section" id="downloadSection" style="display: none;">
                <h3>Recording Complete!</h3>
                <p>Your recording is ready to download.</p>
                <button class="primary-btn" id="downloadBtn">
                    <span class="btn-icon">⬇️</span>
                    Download Recording
                </button>
            </div>
        </main>
    </div>

    <script src="recorder.js"></script>
</body>
</html>
