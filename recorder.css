/* Cursorful Screen Recorder Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-text {
    font-size: 24px;
    font-weight: 300;
    color: #4ade80;
    letter-spacing: 1px;
}

.title {
    font-size: 32px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 40px;
    color: #1f2937;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.step-card {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: flex-start;
    gap: 20px;
    transition: all 0.3s ease;
}

.step-card.active {
    opacity: 1 !important;
    transform: scale(1.02);
    box-shadow: 0 8px 30px rgba(74, 222, 128, 0.2);
}

.step-number {
    background: #4ade80;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1f2937;
}

.step-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 20px;
}

.recording-options {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.option-btn {
    background: #f3f4f6;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
}

.option-btn:hover {
    background: #e5e7eb;
}

.option-btn.selected {
    background: #dcfce7;
    border-color: #4ade80;
    color: #166534;
}

.option-icon {
    font-size: 16px;
}

.primary-btn {
    background: #4ade80;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-btn:hover:not(:disabled) {
    background: #22c55e;
    transform: translateY(-1px);
}

.primary-btn:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    transform: none;
}

.primary-btn.success {
    background: #10b981;
}

.primary-btn.success:hover:not(:disabled) {
    background: #059669;
}

.btn-icon {
    font-size: 18px;
}

/* Recording Controls */
.recording-controls {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-top: 20px;
}

.recording-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
}

.recording-indicator {
    width: 12px;
    height: 12px;
    background: #ef4444;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.recording-text {
    font-size: 18px;
    font-weight: 500;
    color: #1f2937;
}

.recording-timer {
    font-size: 18px;
    font-weight: 600;
    color: #ef4444;
    font-family: 'Courier New', monospace;
}

.control-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.control-btn {
    background: #f3f4f6;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
}

.control-btn:hover {
    background: #e5e7eb;
}

.control-btn.danger {
    background: #fee2e2;
    color: #dc2626;
}

.control-btn.danger:hover {
    background: #fecaca;
}

/* Download Section */
.download-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-top: 20px;
}

.download-section h3 {
    color: #1f2937;
    margin-bottom: 10px;
}

.download-section p {
    color: #6b7280;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 24px;
    }
    
    .step-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
    
    .recording-options {
        justify-content: center;
    }
    
    .control-buttons {
        flex-direction: column;
        align-items: center;
    }
}
