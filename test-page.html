<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursorful Test Page</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .interactive-elements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        button {
            background: #4ade80;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        button:hover {
            background: #22c55e;
            transform: translateY(-2px);
        }

        input, textarea {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
        }

        .moving-element {
            width: 50px;
            height: 50px;
            background: #fbbf24;
            border-radius: 50%;
            position: relative;
            animation: move 3s ease-in-out infinite alternate;
            margin: 20px 0;
        }

        @keyframes move {
            0% { left: 0; }
            100% { left: calc(100% - 50px); }
        }

        .click-counter {
            font-size: 1.2em;
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
        }

        .instructions {
            background: rgba(74, 222, 128, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #4ade80;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Cursorful Test Page</h1>
        
        <div class="instructions">
            <h3>📋 Testing Instructions</h3>
            <p>This page is designed to test the Cursorful screen recorder extension. Try the following:</p>
            <ul>
                <li>Click buttons and interact with elements</li>
                <li>Move your mouse around to test cursor tracking</li>
                <li>Type in the input fields</li>
                <li>Scroll up and down the page</li>
                <li>Watch the moving element animation</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🖱️ Interactive Elements</h3>
            <div class="interactive-elements">
                <button onclick="incrementCounter()">Click Me!</button>
                <button onclick="changeColor()">Change Color</button>
                <button onclick="showAlert()">Show Alert</button>
                <button onclick="addElement()">Add Element</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Form Elements</h3>
            <input type="text" placeholder="Type something here...">
            <textarea rows="4" placeholder="Enter a longer message..."></textarea>
            <label>
                <input type="checkbox"> Check this box
            </label>
        </div>

        <div class="test-section">
            <h3>🎯 Click Counter</h3>
            <div class="click-counter" id="clickCounter">
                Clicks: <span id="clickCount">0</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🎪 Animation Test</h3>
            <p>Watch this moving element:</p>
            <div class="moving-element"></div>
        </div>

        <div class="test-section">
            <h3>📜 Scroll Test</h3>
            <p>This section contains enough content to test scrolling behavior during recording.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        </div>

        <div id="dynamicContent"></div>
    </div>

    <script>
        let clickCount = 0;
        let colorIndex = 0;
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];

        function incrementCounter() {
            clickCount++;
            document.getElementById('clickCount').textContent = clickCount;
        }

        function changeColor() {
            colorIndex = (colorIndex + 1) % colors.length;
            document.body.style.background = `linear-gradient(135deg, ${colors[colorIndex]} 0%, ${colors[(colorIndex + 1) % colors.length]} 100%)`;
        }

        function showAlert() {
            alert('Hello from Cursorful test page! 👋');
        }

        function addElement() {
            const dynamicContent = document.getElementById('dynamicContent');
            const newElement = document.createElement('div');
            newElement.style.cssText = `
                background: rgba(255, 255, 255, 0.2);
                padding: 15px;
                margin: 10px 0;
                border-radius: 8px;
                text-align: center;
            `;
            newElement.textContent = `Dynamic element #${dynamicContent.children.length + 1} - Created at ${new Date().toLocaleTimeString()}`;
            dynamicContent.appendChild(newElement);
        }

        // Add click tracking for testing
        document.addEventListener('click', function(e) {
            console.log('Click detected at:', e.clientX, e.clientY);
        });

        // Add some automatic activity for testing
        setInterval(() => {
            const elements = document.querySelectorAll('.test-section');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        el.style.transform = 'scale(1)';
                    }, 200);
                }, index * 100);
            });
        }, 5000);
    </script>
</body>
</html>
