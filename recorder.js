// Cursorful Screen Recorder JavaScript
class ScreenRecorder {
    constructor() {
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.stream = null;
        this.selectedSource = null;
        this.recordingStartTime = null;
        this.timerInterval = null;
        this.isPaused = false;
        
        this.initializeElements();
        this.attachEventListeners();
        this.checkRecordingState();
    }

    initializeElements() {
        // Source selection buttons
        this.tabBtn = document.getElementById('selectTab');
        this.windowBtn = document.getElementById('selectWindow');
        this.screenBtn = document.getElementById('selectScreen');
        this.selectScreenBtn = document.getElementById('selectScreenBtn');
        
        // Step elements
        this.step2 = document.getElementById('step2');
        this.step3 = document.getElementById('step3');
        this.startRecordingBtn = document.getElementById('startRecordingBtn');
        
        // Recording controls
        this.recordingControls = document.getElementById('recordingControls');
        this.recordingTimer = document.getElementById('recordingTimer');
        this.pauseBtn = document.getElementById('pauseBtn');
        this.stopBtn = document.getElementById('stopBtn');
        
        // Download section
        this.downloadSection = document.getElementById('downloadSection');
        this.downloadBtn = document.getElementById('downloadBtn');
    }

    attachEventListeners() {
        // Source selection
        [this.tabBtn, this.windowBtn, this.screenBtn].forEach(btn => {
            btn.addEventListener('click', (e) => this.selectSource(e.target.dataset.source));
        });
        
        this.selectScreenBtn.addEventListener('click', () => this.requestScreenAccess());
        this.startRecordingBtn.addEventListener('click', () => this.startRecording());
        
        // Recording controls
        this.pauseBtn.addEventListener('click', () => this.togglePause());
        this.stopBtn.addEventListener('click', () => this.stopRecording());
        this.downloadBtn.addEventListener('click', () => this.downloadRecording());

        // Cursor data download button
        const downloadCursorBtn = document.getElementById('downloadCursorBtn');
        if (downloadCursorBtn) {
            downloadCursorBtn.addEventListener('click', () => this.downloadCursorData());
        }

        // Test cursor data button
        const testCursorBtn = document.getElementById('testCursorBtn');
        if (testCursorBtn) {
            testCursorBtn.addEventListener('click', () => this.testCursorData());
        }
    }

    selectSource(source) {
        this.selectedSource = source;
        
        // Update UI
        [this.tabBtn, this.windowBtn, this.screenBtn].forEach(btn => {
            btn.classList.remove('selected');
        });
        
        const selectedBtn = document.querySelector(`[data-source="${source}"]`);
        selectedBtn.classList.add('selected');
        
        this.selectScreenBtn.disabled = false;
        this.selectScreenBtn.textContent = `Select ${source}`;
        
        // Update step 1 as active
        document.querySelector('.step-card').classList.add('active');
    }

    async requestScreenAccess() {
        try {
            const sources = this.getSourcesForSelection();
            
            // Request desktop media access
            const response = await this.sendMessage({
                type: 'GET_DESKTOP_MEDIA',
                sources: sources
            });
            
            if (response.success) {
                await this.setupMediaStream(response.streamId);
                this.activateStep2();
            } else {
                this.showError('Screen access was denied or cancelled.');
            }
        } catch (error) {
            console.error('Error requesting screen access:', error);
            this.showError('Failed to access screen. Please try again.');
        }
    }

    getSourcesForSelection() {
        switch (this.selectedSource) {
            case 'tab': return ['tab'];
            case 'window': return ['window'];
            case 'screen': return ['screen'];
            default: return ['screen', 'window', 'tab'];
        }
    }

    async setupMediaStream(streamId) {
        try {
            // Try with audio first, fallback to video-only if audio fails
            let constraints = {
                audio: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: streamId
                    }
                },
                video: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: streamId,
                        maxWidth: 1920,
                        maxHeight: 1080,
                        maxFrameRate: 30
                    }
                }
            };

            try {
                this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                console.log('Media stream obtained successfully with audio');
            } catch (audioError) {
                console.warn('Audio capture failed, trying video-only:', audioError);
                // Fallback to video-only
                constraints = {
                    video: {
                        mandatory: {
                            chromeMediaSource: 'desktop',
                            chromeMediaSourceId: streamId,
                            maxWidth: 1920,
                            maxHeight: 1080,
                            maxFrameRate: 30
                        }
                    }
                };
                this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                console.log('Media stream obtained successfully (video-only)');
            }
        } catch (error) {
            console.error('Error setting up media stream:', error);
            throw error;
        }
    }

    activateStep2() {
        this.step2.style.opacity = '1';
        this.step2.classList.add('active');
        
        // Auto-advance to step 3 after a short delay
        setTimeout(() => {
            this.activateStep3();
        }, 1500);
    }

    activateStep3() {
        this.step3.style.opacity = '1';
        this.step3.classList.add('active');
        this.startRecordingBtn.disabled = false;
    }

    async startRecording() {
        try {
            if (!this.stream) {
                throw new Error('No media stream available');
            }

            // Setup MediaRecorder with fallback mime types
            let mimeType = 'video/webm;codecs=vp9';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
                mimeType = 'video/webm;codecs=vp8';
                if (!MediaRecorder.isTypeSupported(mimeType)) {
                    mimeType = 'video/webm';
                }
            }

            this.mediaRecorder = new MediaRecorder(this.stream, { mimeType });

            this.recordedChunks = [];
            this.recordingStartTime = Date.now();

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.handleRecordingComplete();
            };

            this.mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event.error);
                this.showError('Recording error occurred');
            };

            // Start recording
            this.mediaRecorder.start(1000); // Collect data every second

            // Update UI
            this.showRecordingControls();
            this.startTimer();

            // Notify background script and start cursor tracking
            await this.sendMessage({ type: 'START_RECORDING' });
            await this.startCursorTracking();

            console.log('Recording started successfully with mime type:', mimeType);
        } catch (error) {
            console.error('Error starting recording:', error);
            this.showError('Failed to start recording. Please try again.');
        }
    }

    showRecordingControls() {
        // Hide steps
        document.querySelector('.steps-container').style.display = 'none';
        
        // Show recording controls
        this.recordingControls.style.display = 'block';
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            if (!this.isPaused) {
                const elapsed = Date.now() - this.recordingStartTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                this.recordingTimer.textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }

    togglePause() {
        if (this.isPaused) {
            this.mediaRecorder.resume();
            this.pauseBtn.innerHTML = '<span class="btn-icon">⏸️</span>Pause';
            this.isPaused = false;
        } else {
            this.mediaRecorder.pause();
            this.pauseBtn.innerHTML = '<span class="btn-icon">▶️</span>Resume';
            this.isPaused = true;
        }
    }

    async stopRecording() {
        try {
            if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                this.mediaRecorder.stop();
            }

            if (this.stream) {
                this.stream.getTracks().forEach(track => track.stop());
            }

            if (this.timerInterval) {
                clearInterval(this.timerInterval);
            }

            // Stop cursor tracking and notify background script
            await this.stopCursorTracking();
            await this.sendMessage({ type: 'STOP_RECORDING' });

        } catch (error) {
            console.error('Error stopping recording:', error);
        }
    }

    handleRecordingComplete() {
        // Hide recording controls
        this.recordingControls.style.display = 'none';
        
        // Show download section
        this.downloadSection.style.display = 'block';
        
        console.log('Recording completed, ready for download');
    }

    downloadRecording() {
        if (this.recordedChunks.length === 0) {
            this.showError('No recording data available');
            return;
        }

        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `cursorful-recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);

        console.log('Recording download initiated');
    }

    async downloadCursorData() {
        try {
            // Get cursor data from all tabs
            const tabs = await chrome.tabs.query({});
            let allCursorData = [];

            for (const tab of tabs) {
                try {
                    const response = await chrome.tabs.sendMessage(tab.id, {
                        type: 'GET_CURSOR_DATA'
                    });

                    console.log(`Tab ${tab.id} cursor data response:`, response);

                    if (response && response.data && response.data.length > 0) {
                        allCursorData.push({
                            tabId: tab.id,
                            tabUrl: tab.url,
                            tabTitle: tab.title,
                            data: response.data
                        });
                        console.log(`Added ${response.data.length} cursor data points from tab ${tab.id}`);
                    }
                } catch (error) {
                    console.log(`Could not get cursor data from tab ${tab.id}:`, error.message);
                    continue;
                }
            }

            if (allCursorData.length === 0) {
                this.showError('No cursor data available. Make sure recording was active.');
                return;
            }

            // Combine all data and create export
            const exportData = {
                metadata: {
                    exportTime: new Date().toISOString(),
                    totalTabs: allCursorData.length,
                    totalDataPoints: allCursorData.reduce((sum, tab) => sum + tab.data.length, 0),
                    userAgent: navigator.userAgent
                },
                tabs: allCursorData
            };

            // Create and download JSON file
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cursor-data-${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            console.log('Cursor data download initiated:', exportData.metadata);

        } catch (error) {
            console.error('Error downloading cursor data:', error);
            this.showError('Failed to download cursor data: ' + error.message);
        }
    }

    async testCursorData() {
        try {
            console.log('Testing cursor data collection...');

            // Get cursor data from all tabs
            const tabs = await chrome.tabs.query({});
            let totalDataPoints = 0;

            for (const tab of tabs) {
                try {
                    const response = await chrome.tabs.sendMessage(tab.id, {
                        type: 'TEST_CURSOR_TRACKING'
                    });

                    if (response) {
                        console.log(`Tab ${tab.id} (${tab.url}):`, response);
                        totalDataPoints += response.dataPoints || 0;
                    }
                } catch (error) {
                    console.log(`Tab ${tab.id} not accessible:`, error.message);
                }
            }

            alert(`Cursor tracking test complete!\n\nTotal data points across all tabs: ${totalDataPoints}\n\nCheck browser console for detailed results.`);

        } catch (error) {
            console.error('Error testing cursor data:', error);
            alert('Error testing cursor data: ' + error.message);
        }
    }

    async checkRecordingState() {
        // Check if there's an ongoing recording
        try {
            const result = await chrome.storage.local.get(['isRecording']);
            if (result.isRecording) {
                // Show appropriate UI for ongoing recording
                this.showRecordingControls();
            }
        } catch (error) {
            console.error('Error checking recording state:', error);
        }
    }

    async startCursorTracking() {
        try {
            // Get all tabs and send cursor tracking start message
            const tabs = await chrome.tabs.query({});
            for (const tab of tabs) {
                try {
                    await chrome.tabs.sendMessage(tab.id, { type: 'START_CURSOR_TRACKING' });
                } catch (error) {
                    // Ignore errors for tabs that can't receive messages
                    console.log('Could not send cursor tracking message to tab:', tab.id);
                }
            }
        } catch (error) {
            console.error('Error starting cursor tracking:', error);
        }
    }

    async stopCursorTracking() {
        try {
            // Get all tabs and send cursor tracking stop message
            const tabs = await chrome.tabs.query({});
            for (const tab of tabs) {
                try {
                    await chrome.tabs.sendMessage(tab.id, { type: 'STOP_CURSOR_TRACKING' });
                } catch (error) {
                    // Ignore errors for tabs that can't receive messages
                    console.log('Could not send cursor tracking stop message to tab:', tab.id);
                }
            }
        } catch (error) {
            console.error('Error stopping cursor tracking:', error);
        }
    }

    sendMessage(message) {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage(message, resolve);
        });
    }

    showError(message) {
        // Create a more user-friendly error display
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #fee2e2;
            color: #dc2626;
            padding: 12px 20px;
            border-radius: 8px;
            border: 1px solid #fecaca;
            z-index: 10000;
            font-family: inherit;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;
        errorDiv.textContent = message;

        document.body.appendChild(errorDiv);

        // Remove error after 5 seconds
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
}

// Initialize the recorder when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ScreenRecorder();
});
