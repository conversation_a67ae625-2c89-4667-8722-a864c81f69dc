# Cursorful Screen Recorder

A Chrome extension for recording browser screens with cursor tracking functionality. Record individual tabs, application windows, or the entire screen with an intuitive three-step process.

## Features

- **Multiple Recording Sources**: Choose from browser tabs, application windows, or entire screen
- **Cursor Tracking**: Enhanced cursor visibility and click indicators during recording
- **Clean Interface**: Intuitive three-step recording process
- **Recording Controls**: Start, pause, and stop recordings with visual feedback
- **Download Support**: Save recordings as WebM files
- **Recording Indicators**: Visual feedback showing recording status
- **Manifest V3**: Built with the latest Chrome extension standards

## Installation

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension directory
5. The Cursorful icon should appear in your extensions toolbar

## Usage

### Starting a Recording

1. Click the Cursorful extension icon in your toolbar
2. **Step 1**: Select your recording source (tab, window, or screen) and click "Select screen"
3. **Step 2**: The screen sharing bar will be hidden automatically
4. **Step 3**: Click "Start recording" to begin

### During Recording

- A recording indicator appears in the top-right corner of pages
- Enhanced cursor visibility with green highlight
- Click indicators show where you click
- Use the recording controls to pause or stop

### Stopping and Downloading

- Click the "Stop" button in the recording controls
- Your recording will be processed and ready for download
- Click "Download Recording" to save the WebM file

## File Structure

```
chromerecord/
├── manifest.json          # Extension manifest (V3)
├── background.js          # Service worker for extension logic
├── recorder.html          # Main recording interface
├── recorder.css           # Styles for the recording interface
├── recorder.js            # Recording functionality and UI logic
├── content.js             # Content script for cursor tracking
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # This file
```

## Technical Details

### Permissions

- `activeTab`: Access to the current active tab
- `desktopCapture`: Screen recording capabilities
- `storage`: Storing recording state
- `tabs`: Managing tab information

### APIs Used

- **Screen Capture API**: For recording screen content
- **MediaRecorder API**: For processing and saving recordings
- **Chrome Extension APIs**: For extension functionality
- **Desktop Capture API**: For selecting recording sources

### Browser Compatibility

- Chrome 88+ (Manifest V3 support)
- Chromium-based browsers with extension support

## Development

### Building

No build process required - this is a vanilla JavaScript extension.

### Testing

1. Load the extension in developer mode
2. Test recording functionality with different sources
3. Verify cursor tracking works on various websites
4. Check recording quality and file output

### Debugging

- Use Chrome DevTools for the recorder page
- Check the background script in `chrome://extensions/`
- Monitor console logs for error messages

## Troubleshooting

### Recording Not Starting

- Ensure you've granted screen capture permissions
- Check that the selected source is available
- Verify your browser supports the MediaRecorder API

### Cursor Tracking Not Working

- Some websites may block content scripts
- Cursor tracking works best on regular web pages
- System-level cursor tracking is limited by browser security

### Download Issues

- Ensure your browser allows file downloads
- Check available disk space
- Try a different recording duration

## Privacy

- No data is sent to external servers
- Recordings are processed locally in your browser
- Extension only accesses tabs when recording is active

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review browser console for error messages
3. Ensure you're using a supported Chrome version
4. Verify all permissions are granted
