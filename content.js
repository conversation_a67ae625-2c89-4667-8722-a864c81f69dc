// Content script for Cursorful Screen Recorder
// Handles cursor tracking and recording indicators

class CursorTracker {
    constructor() {
        this.isTracking = false;
        this.cursorElement = null;
        this.recordingIndicator = null;
        this.mousePosition = { x: 0, y: 0 };
        this.clickIndicators = [];
        
        this.init();
    }

    init() {
        // Listen for messages from background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
        });

        // Check if recording is active on page load
        this.checkRecordingStatus();
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.type) {
            case 'START_CURSOR_TRACKING':
                this.startCursorTracking();
                sendResponse({ success: true });
                break;
            case 'STOP_CURSOR_TRACKING':
                this.stopCursorTracking();
                sendResponse({ success: true });
                break;
            case 'RECORDING_ACTIVE':
                this.showRecordingIndicator();
                sendResponse({ success: true });
                break;
            case 'RECORDING_STOPPED':
                this.hideRecordingIndicator();
                sendResponse({ success: true });
                break;
        }
    }

    async checkRecordingStatus() {
        try {
            const result = await chrome.storage.local.get(['isRecording', 'recordingTabId']);
            if (result.isRecording) {
                // Get current tab ID
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (tabs[0] && tabs[0].id === result.recordingTabId) {
                    this.showRecordingIndicator();
                    this.startCursorTracking();
                }
            }
        } catch (error) {
            console.error('Error checking recording status:', error);
        }
    }

    startCursorTracking() {
        if (this.isTracking) return;
        
        this.isTracking = true;
        this.createCursorElement();
        this.attachMouseListeners();
        
        console.log('Cursor tracking started');
    }

    stopCursorTracking() {
        if (!this.isTracking) return;
        
        this.isTracking = false;
        this.removeCursorElement();
        this.removeMouseListeners();
        this.clearClickIndicators();
        
        console.log('Cursor tracking stopped');
    }

    createCursorElement() {
        // Create enhanced cursor element
        this.cursorElement = document.createElement('div');
        this.cursorElement.id = 'cursorful-cursor';
        this.cursorElement.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            border: 2px solid #4ade80;
            border-radius: 50%;
            background: rgba(74, 222, 128, 0.2);
            pointer-events: none;
            z-index: 999999;
            transition: all 0.1s ease;
            box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
        `;
        
        document.body.appendChild(this.cursorElement);
    }

    removeCursorElement() {
        if (this.cursorElement) {
            this.cursorElement.remove();
            this.cursorElement = null;
        }
    }

    attachMouseListeners() {
        this.mouseMoveHandler = (e) => this.handleMouseMove(e);
        this.mouseClickHandler = (e) => this.handleMouseClick(e);
        this.mouseDownHandler = (e) => this.handleMouseDown(e);
        this.mouseUpHandler = (e) => this.handleMouseUp(e);

        document.addEventListener('mousemove', this.mouseMoveHandler, true);
        document.addEventListener('click', this.mouseClickHandler, true);
        document.addEventListener('mousedown', this.mouseDownHandler, true);
        document.addEventListener('mouseup', this.mouseUpHandler, true);
    }

    removeMouseListeners() {
        if (this.mouseMoveHandler) {
            document.removeEventListener('mousemove', this.mouseMoveHandler, true);
            document.removeEventListener('click', this.mouseClickHandler, true);
            document.removeEventListener('mousedown', this.mouseDownHandler, true);
            document.removeEventListener('mouseup', this.mouseUpHandler, true);
        }
    }

    handleMouseMove(e) {
        this.mousePosition = { x: e.clientX, y: e.clientY };
        
        if (this.cursorElement) {
            this.cursorElement.style.left = (e.clientX - 10) + 'px';
            this.cursorElement.style.top = (e.clientY - 10) + 'px';
        }
    }

    handleMouseClick(e) {
        this.createClickIndicator(e.clientX, e.clientY);
    }

    handleMouseDown(e) {
        if (this.cursorElement) {
            this.cursorElement.style.transform = 'scale(0.8)';
            this.cursorElement.style.background = 'rgba(74, 222, 128, 0.4)';
        }
    }

    handleMouseUp(e) {
        if (this.cursorElement) {
            this.cursorElement.style.transform = 'scale(1)';
            this.cursorElement.style.background = 'rgba(74, 222, 128, 0.2)';
        }
    }

    createClickIndicator(x, y) {
        const indicator = document.createElement('div');
        indicator.style.cssText = `
            position: fixed;
            left: ${x - 15}px;
            top: ${y - 15}px;
            width: 30px;
            height: 30px;
            border: 3px solid #4ade80;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999998;
            animation: cursorful-click-pulse 0.6s ease-out forwards;
        `;

        // Add CSS animation if not already added
        if (!document.getElementById('cursorful-styles')) {
            const style = document.createElement('style');
            style.id = 'cursorful-styles';
            style.textContent = `
                @keyframes cursorful-click-pulse {
                    0% {
                        transform: scale(0.5);
                        opacity: 1;
                    }
                    100% {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(indicator);
        this.clickIndicators.push(indicator);

        // Remove indicator after animation
        setTimeout(() => {
            indicator.remove();
            const index = this.clickIndicators.indexOf(indicator);
            if (index > -1) {
                this.clickIndicators.splice(index, 1);
            }
        }, 600);
    }

    clearClickIndicators() {
        this.clickIndicators.forEach(indicator => indicator.remove());
        this.clickIndicators = [];
    }

    showRecordingIndicator() {
        if (this.recordingIndicator) return;

        this.recordingIndicator = document.createElement('div');
        this.recordingIndicator.id = 'cursorful-recording-indicator';
        this.recordingIndicator.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 8px; height: 8px; background: #ef4444; border-radius: 50%; animation: cursorful-pulse 1.5s infinite;"></div>
                <span style="font-size: 12px; font-weight: 500;">Recording</span>
            </div>
        `;
        
        this.recordingIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            color: #1f2937;
            padding: 8px 12px;
            border-radius: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 999997;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        `;

        // Add pulse animation if not already added
        if (!document.getElementById('cursorful-pulse-styles')) {
            const style = document.createElement('style');
            style.id = 'cursorful-pulse-styles';
            style.textContent = `
                @keyframes cursorful-pulse {
                    0%, 100% { opacity: 1; }
                    50% { opacity: 0.5; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(this.recordingIndicator);
    }

    hideRecordingIndicator() {
        if (this.recordingIndicator) {
            this.recordingIndicator.remove();
            this.recordingIndicator = null;
        }
    }
}

// Initialize cursor tracker
const cursorTracker = new CursorTracker();
