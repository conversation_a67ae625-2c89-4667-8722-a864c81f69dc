// Background service worker for Cursorful Screen Recorder
chrome.runtime.onInstalled.addListener(() => {
  console.log('Cursorful Screen Recorder installed');
});

// Handle extension icon click - redirect to recorder page
chrome.action.onClicked.addListener((tab) => {
  chrome.tabs.create({
    url: chrome.runtime.getURL('recorder.html')
  });
});

// Handle messages from content scripts and recorder page
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'START_RECORDING':
      handleStartRecording(message, sender, sendResponse);
      break;
    case 'STOP_RECORDING':
      handleStopRecording(message, sender, sendResponse);
      break;
    case 'GET_DESKTOP_MEDIA':
      handleGetDesktopMedia(message, sender, sendResponse);
      break;
    default:
      console.log('Unknown message type:', message.type);
  }
  return true; // Keep message channel open for async response
});

// Handle desktop capture request
function handleGetDesktopMedia(message, sender, sendResponse) {
  const sources = message.sources || ['screen', 'window', 'tab'];
  
  chrome.desktopCapture.chooseDesktopMedia(
    sources,
    sender.tab,
    (streamId) => {
      if (streamId) {
        sendResponse({ success: true, streamId: streamId });
      } else {
        sendResponse({ success: false, error: 'User cancelled or no stream available' });
      }
    }
  );
}

// Handle start recording request
function handleStartRecording(message, sender, sendResponse) {
  // Store recording state
  chrome.storage.local.set({
    isRecording: true,
    recordingStartTime: Date.now(),
    recordingTabId: sender.tab.id
  });

  // Update extension badge
  chrome.action.setBadgeText({ text: 'REC' });
  chrome.action.setBadgeBackgroundColor({ color: '#ff4444' });

  // Notify all tabs about recording start
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      chrome.tabs.sendMessage(tab.id, { type: 'RECORDING_ACTIVE' }).catch(() => {
        // Ignore errors for tabs that can't receive messages
      });
    });
  });

  sendResponse({ success: true });
}

// Handle stop recording request
function handleStopRecording(message, sender, sendResponse) {
  // Clear recording state
  chrome.storage.local.set({
    isRecording: false,
    recordingStartTime: null,
    recordingTabId: null
  });

  // Clear extension badge
  chrome.action.setBadgeText({ text: '' });

  // Notify all tabs about recording stop
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      chrome.tabs.sendMessage(tab.id, { type: 'RECORDING_STOPPED' }).catch(() => {
        // Ignore errors for tabs that can't receive messages
      });
    });
  });

  sendResponse({ success: true });
}

// Handle tab updates to manage recording state
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    // Check if this tab has an active recording
    chrome.storage.local.get(['isRecording', 'recordingTabId'], (result) => {
      if (result.isRecording && result.recordingTabId === tabId) {
        // Inject recording indicator if needed
        chrome.tabs.sendMessage(tabId, { type: 'RECORDING_ACTIVE' });
      }
    });
  }
});

// Handle tab removal
chrome.tabs.onRemoved.addListener((tabId) => {
  chrome.storage.local.get(['recordingTabId'], (result) => {
    if (result.recordingTabId === tabId) {
      // Stop recording if the recording tab was closed
      handleStopRecording({}, {}, () => {});
    }
  });
});
