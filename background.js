// Background service worker for Cursorful Screen Recorder
// Mouse data storage
let mouseDataStorage = new Map(); // sessionId -> mouseData

chrome.runtime.onInstalled.addListener(() => {
  console.log('Cursorful Screen Recorder installed');
});

// Handle extension icon click - redirect to recorder page
chrome.action.onClicked.addListener((tab) => {
  chrome.tabs.create({
    url: chrome.runtime.getURL('recorder.html')
  });
});

// Handle messages from content scripts and recorder page
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'START_RECORDING':
      handleStartRecording(message, sender, sendResponse);
      break;
    case 'STOP_RECORDING':
      handleStopRecording(message, sender, sendResponse);
      break;
    case 'GET_DESKTOP_MEDIA':
      handleGetDesktopMedia(message, sender, sendResponse);
      break;
    case 'MOUSE_DATA':
      handleMouseData(message, sender, sendResponse);
      break;
    case 'GET_MOUSE_DATA':
      handleGetMouseData(message, sender, sendResponse);
      break;
    case 'CLEAR_MOUSE_DATA':
      handleClearMouseData(message, sender, sendResponse);
      break;
    default:
      console.log('Unknown message type:', message.type);
  }
  return true; // Keep message channel open for async response
});

// Handle desktop capture request
function handleGetDesktopMedia(message, sender, sendResponse) {
  const sources = message.sources || ['screen', 'window', 'tab'];
  
  chrome.desktopCapture.chooseDesktopMedia(
    sources,
    sender.tab,
    (streamId) => {
      if (streamId) {
        sendResponse({ success: true, streamId: streamId });
      } else {
        sendResponse({ success: false, error: 'User cancelled or no stream available' });
      }
    }
  );
}

// Handle start recording request
function handleStartRecording(message, sender, sendResponse) {
  const sessionId = generateSessionId();
  const recordingStartTime = Date.now();

  // Store recording state
  chrome.storage.local.set({
    isRecording: true,
    recordingStartTime: recordingStartTime,
    recordingTabId: sender.tab.id,
    sessionId: sessionId
  });

  // Initialize mouse data storage for this session
  mouseDataStorage.set(sessionId, {
    sessionId: sessionId,
    recordingStartTime: recordingStartTime,
    recordingTabId: sender.tab.id,
    mouseEvents: [],
    screenDimensions: null
  });

  // Update extension badge
  chrome.action.setBadgeText({ text: 'REC' });
  chrome.action.setBadgeBackgroundColor({ color: '#ff4444' });

  // Only start cursor tracking on the recording tab
  chrome.tabs.sendMessage(sender.tab.id, {
    type: 'START_CURSOR_TRACKING',
    sessionId: sessionId,
    recordingStartTime: recordingStartTime
  }).catch(() => {
    console.log('Could not send cursor tracking message to recording tab');
  });

  // Notify all tabs about recording status
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      if (tab.id === sender.tab.id) {
        chrome.tabs.sendMessage(tab.id, { type: 'RECORDING_ACTIVE' }).catch(() => {});
      }
    });
  });

  sendResponse({ success: true, sessionId: sessionId });
}

// Handle stop recording request
function handleStopRecording(message, sender, sendResponse) {
  // Get current session info before clearing
  chrome.storage.local.get(['sessionId', 'recordingTabId'], (result) => {
    const sessionId = result.sessionId;
    const recordingTabId = result.recordingTabId;

    // Clear recording state
    chrome.storage.local.set({
      isRecording: false,
      recordingStartTime: null,
      recordingTabId: null,
      sessionId: null
    });

    // Clear extension badge
    chrome.action.setBadgeText({ text: '' });

    // Stop cursor tracking on the recording tab
    if (recordingTabId) {
      chrome.tabs.sendMessage(recordingTabId, { type: 'STOP_CURSOR_TRACKING' }).catch(() => {
        console.log('Could not send stop cursor tracking message to recording tab');
      });
    }

    // Notify all tabs about recording stop
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        chrome.tabs.sendMessage(tab.id, { type: 'RECORDING_STOPPED' }).catch(() => {
          // Ignore errors for tabs that can't receive messages
        });
      });
    });

    sendResponse({ success: true, sessionId: sessionId });
  });
}

// Handle tab updates to manage recording state
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    // Check if this tab has an active recording
    chrome.storage.local.get(['isRecording', 'recordingTabId', 'sessionId', 'recordingStartTime'], (result) => {
      if (result.isRecording && result.recordingTabId === tabId) {
        // Start cursor tracking on this specific tab only
        chrome.tabs.sendMessage(tabId, {
          type: 'START_CURSOR_TRACKING',
          sessionId: result.sessionId,
          recordingStartTime: result.recordingStartTime
        }).catch(() => {
          console.log('Could not restart cursor tracking on tab reload');
        });
      }
    });
  }
});

// Handle tab removal
chrome.tabs.onRemoved.addListener((tabId) => {
  chrome.storage.local.get(['recordingTabId'], (result) => {
    if (result.recordingTabId === tabId) {
      // Stop recording if the recording tab was closed
      handleStopRecording({}, {}, () => {});
    }
  });
});

// Mouse data handling functions
function handleMouseData(message, sender, sendResponse) {
  const data = message.data;
  if (!data || !data.sessionId) {
    sendResponse({ success: false, error: 'Invalid mouse data' });
    return;
  }

  // Get or create session data
  let sessionData = mouseDataStorage.get(data.sessionId);
  if (!sessionData) {
    sessionData = {
      sessionId: data.sessionId,
      recordingStartTime: data.recordingStartTime,
      recordingTabId: sender.tab.id,
      mouseEvents: [],
      screenDimensions: data.screenDimensions
    };
    mouseDataStorage.set(data.sessionId, sessionData);
  }

  // Append new mouse events
  if (data.mouseEvents && data.mouseEvents.length > 0) {
    sessionData.mouseEvents.push(...data.mouseEvents);
    sessionData.screenDimensions = data.screenDimensions; // Update dimensions
  }

  console.log(`Received ${data.mouseEvents.length} mouse events for session ${data.sessionId}. Total: ${sessionData.mouseEvents.length}`);
  sendResponse({ success: true });
}

function handleGetMouseData(message, sender, sendResponse) {
  const sessionId = message.sessionId;
  if (!sessionId) {
    // Return all sessions if no specific session requested
    const allData = Array.from(mouseDataStorage.values());
    sendResponse({ success: true, data: allData });
    return;
  }

  const sessionData = mouseDataStorage.get(sessionId);
  if (sessionData) {
    sendResponse({ success: true, data: sessionData });
  } else {
    sendResponse({ success: false, error: 'Session not found' });
  }
}

function handleClearMouseData(message, sender, sendResponse) {
  const sessionId = message.sessionId;
  if (sessionId) {
    mouseDataStorage.delete(sessionId);
    console.log(`Cleared mouse data for session ${sessionId}`);
  } else {
    mouseDataStorage.clear();
    console.log('Cleared all mouse data');
  }
  sendResponse({ success: true });
}

function generateSessionId() {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}
